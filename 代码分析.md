# Claude模型请求转换代理分析

## 项目概述

这是一个基于FastAPI的代理服务器，用于将Anthropic Claude API格式的请求转换为OpenAI或Gemini API格式。该代理允许使用Claude客户端（如Claude Code）来调用OpenAI或Gemini的模型，实现了API格式的无缝转换。

## 核心架构

```
<PERSON>客户端 → 代理服务器 → LiteLLM → OpenAI/Gemini API
     ↑                                        ↓
     ←─────── 响应格式转换 ←─────────────────────
```

## API格式差异分析

### 1. Claude (Anthropic) API 格式

**请求结构：**
```json
{
  "model": "claude-3-sonnet-20240229",
  "max_tokens": 1000,
  "system": "你是一个有用的助手",  // 可以是字符串或内容块数组
  "messages": [
    {
      "role": "user",
      "content": [  // 可以是字符串或内容块数组
        {"type": "text", "text": "Hello"},
        {"type": "image", "source": {...}},
        {"type": "tool_result", "tool_use_id": "...", "content": "..."}
      ]
    }
  ],
  "tools": [
    {
      "name": "calculator",
      "description": "计算数学表达式",
      "input_schema": {  // JSON Schema格式
        "type": "object",
        "properties": {...}
      }
    }
  ],
  "tool_choice": {"type": "auto"},  // 或 {"type": "tool", "name": "function_name"}
  "temperature": 0.7,
  "top_p": 0.9,
  "top_k": 40,
  "stop_sequences": ["停止词"],
  "stream": false
}
```

**响应结构：**
```json
{
  "id": "msg_123",
  "model": "claude-3-sonnet-20240229",
  "role": "assistant",
  "content": [
    {"type": "text", "text": "响应文本"},
    {"type": "tool_use", "id": "tool_123", "name": "calculator", "input": {...}}
  ],
  "stop_reason": "end_turn",  // 或 "max_tokens", "tool_use"
  "usage": {
    "input_tokens": 100,
    "output_tokens": 50
  }
}
```

### 2. OpenAI API 格式

**请求结构：**
```json
{
  "model": "gpt-4",
  "max_tokens": 1000,
  "messages": [
    {"role": "system", "content": "你是一个有用的助手"},  // 系统消息作为普通消息
    {"role": "user", "content": "Hello"}  // 内容通常是字符串
  ],
  "tools": [
    {
      "type": "function",  // 固定为 "function"
      "function": {
        "name": "calculator",
        "description": "计算数学表达式",
        "parameters": {  // 使用 "parameters" 而不是 "input_schema"
          "type": "object",
          "properties": {...}
        }
      }
    }
  ],
  "tool_choice": "auto",  // 或 {"type": "function", "function": {"name": "..."}}
  "temperature": 0.7,
  "top_p": 0.9,
  // 注意：OpenAI不支持 top_k
  "stop": ["停止词"],  // 使用 "stop" 而不是 "stop_sequences"
  "stream": false
}
```

**响应结构：**
```json
{
  "id": "chatcmpl-123",
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "响应文本",
        "tool_calls": [  // 工具调用结构不同
          {
            "id": "call_123",
            "type": "function",
            "function": {
              "name": "calculator",
              "arguments": "{\"expression\": \"2+2\"}"  // JSON字符串
            }
          }
        ]
      },
      "finish_reason": "stop"  // 或 "length", "tool_calls"
    }
  ],
  "usage": {
    "prompt_tokens": 100,
    "completion_tokens": 50
  }
}
```

### 3. Gemini API 格式

Gemini API格式与OpenAI类似，但有额外限制：

**Schema限制：**
- 不支持 `additionalProperties` 字段
- 不支持 `default` 字段  
- 字符串类型的 `format` 字段只支持 "enum" 和 "date-time"

## 模型映射策略

### 智能模型映射

代理实现了智能的模型名称映射：

```python
# 映射逻辑
if 'haiku' in model_name.lower():
    # haiku 映射到小模型
    if PREFERRED_PROVIDER == "google":
        mapped_model = f"gemini/{SMALL_MODEL}"  # 默认: gemini-2.0-flash
    else:
        mapped_model = f"openai/{SMALL_MODEL}"   # 默认: gpt-4.1-mini

elif 'sonnet' in model_name.lower():
    # sonnet 映射到大模型
    if PREFERRED_PROVIDER == "google":
        mapped_model = f"gemini/{BIG_MODEL}"     # 默认: gemini-2.5-pro-preview-03-25
    else:
        mapped_model = f"openai/{BIG_MODEL}"     # 默认: gpt-4.1
```

### 配置参数

- `PREFERRED_PROVIDER`: "openai" (默认) 或 "google"
- `BIG_MODEL`: 大模型名称（sonnet映射目标）
- `SMALL_MODEL`: 小模型名称（haiku映射目标）

## 请求转换过程

### 1. 系统消息处理

```python
# Claude格式 → OpenAI格式
if isinstance(system, str):
    messages.append({"role": "system", "content": system})
elif isinstance(system, list):
    # 合并多个系统内容块
    system_text = "".join([block.text for block in system if block.type == "text"])
    messages.append({"role": "system", "content": system_text})
```

### 2. 内容块转换

**复杂内容块处理：**
```python
# tool_result 块转换为纯文本（OpenAI不支持）
if block.type == "tool_result":
    text_content += f"Tool result for {block.tool_use_id}:\n{block.content}\n"

# tool_use 块保留（用于助手消息）
elif block.type == "tool_use":
    processed_content.append({
        "type": "tool_use",
        "id": block.id,
        "name": block.name,
        "input": block.input
    })

# 图像块处理
elif block.type == "image":
    processed_content.append({"type": "image", "source": block.source})
```

### 3. 工具定义转换

```python
# Claude工具 → OpenAI工具
claude_tool = {
    "name": "calculator",
    "description": "计算器",
    "input_schema": {...}
}

openai_tool = {
    "type": "function",
    "function": {
        "name": claude_tool["name"],
        "description": claude_tool["description"],
        "parameters": claude_tool["input_schema"]  # 重命名字段
    }
}

# Gemini需要额外的schema清理
if is_gemini_model:
    parameters = clean_gemini_schema(parameters)
```

### 4. Gemini Schema清理

```python
def clean_gemini_schema(schema):
    """清理Gemini不支持的schema字段"""
    if isinstance(schema, dict):
        # 移除不支持的字段
        schema.pop("additionalProperties", None)
        schema.pop("default", None)
        
        # 处理字符串格式限制
        if schema.get("type") == "string" and "format" in schema:
            allowed_formats = {"enum", "date-time"}
            if schema["format"] not in allowed_formats:
                schema.pop("format")
        
        # 递归清理嵌套对象
        for key, value in schema.items():
            schema[key] = clean_gemini_schema(value)
    
    return schema
```

## 响应转换过程

### 1. 基本结构映射

```python
# OpenAI响应 → Claude响应
openai_response = {
    "choices": [{"message": {"content": "...", "tool_calls": [...]}}],
    "usage": {"prompt_tokens": 100, "completion_tokens": 50}
}

claude_response = {
    "content": [
        {"type": "text", "text": "..."},
        {"type": "tool_use", "id": "...", "name": "...", "input": {...}}
    ],
    "usage": {
        "input_tokens": 100,    # prompt_tokens
        "output_tokens": 50     # completion_tokens
    }
}
```

### 2. 工具调用转换

```python
# OpenAI tool_calls → Claude tool_use
for tool_call in openai_tool_calls:
    claude_content.append({
        "type": "tool_use",
        "id": tool_call.id,
        "name": tool_call.function.name,
        "input": json.loads(tool_call.function.arguments)  # 解析JSON字符串
    })
```

### 3. 停止原因映射

```python
# finish_reason → stop_reason
stop_reason_map = {
    "stop": "end_turn",
    "length": "max_tokens", 
    "tool_calls": "tool_use"
}
```

## 流式响应处理

### Claude流式格式

Claude使用Server-Sent Events (SSE)，包含以下事件类型：

```
event: message_start
data: {"type": "message_start", "message": {...}}

event: content_block_start  
data: {"type": "content_block_start", "index": 0, "content_block": {"type": "text"}}

event: content_block_delta
data: {"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": "Hello"}}

event: content_block_stop
data: {"type": "content_block_stop", "index": 0}

event: message_delta
data: {"type": "message_delta", "delta": {"stop_reason": "end_turn"}, "usage": {...}}

event: message_stop
data: {"type": "message_stop"}
```

### OpenAI流式格式

OpenAI使用增量更新：

```
data: {"choices": [{"delta": {"content": "Hello"}}]}
data: {"choices": [{"delta": {"tool_calls": [{"function": {"arguments": "..."}}]}}]}
data: {"choices": [{"finish_reason": "stop"}]}
```

### 流式转换逻辑

```python
async def handle_streaming(openai_stream, original_request):
    # 发送message_start事件
    yield f"event: message_start\ndata: {json.dumps(message_start_data)}\n\n"
    
    # 发送content_block_start事件
    yield f"event: content_block_start\ndata: {json.dumps(content_block_start)}\n\n"
    
    async for chunk in openai_stream:
        if chunk.choices[0].delta.content:
            # 文本增量 → content_block_delta
            delta_data = {
                "type": "content_block_delta",
                "index": 0,
                "delta": {"type": "text_delta", "text": chunk.choices[0].delta.content}
            }
            yield f"event: content_block_delta\ndata: {json.dumps(delta_data)}\n\n"
        
        if chunk.choices[0].delta.tool_calls:
            # 工具调用 → tool_use块
            # 处理工具调用的流式更新...
```

## 特殊处理机制

### 1. OpenAI模型的内容扁平化

由于OpenAI不支持复杂的内容块，代理会将所有内容扁平化为字符串：

```python
# 内容块扁平化
if msg["role"] == "user" and has_tool_result_blocks:
    # 将tool_result转换为纯文本
    text_content = ""
    for block in content_blocks:
        if block["type"] == "tool_result":
            text_content += f"Tool Result:\n{extract_text(block.content)}\n"
    
    msg["content"] = text_content
```

### 2. 令牌限制处理

```python
# OpenAI/Gemini模型的令牌限制
if model.startswith("openai/") or model.startswith("gemini/"):
    max_tokens = min(max_tokens, 16384)  # 限制最大令牌数
```

### 3. 错误处理和降级

```python
# 转换失败时的降级处理
try:
    converted_response = convert_litellm_to_anthropic(response, request)
except Exception as e:
    # 返回错误响应，保持Claude格式
    return MessagesResponse(
        id=f"msg_{uuid.uuid4()}",
        content=[{"type": "text", "text": f"Error: {str(e)}"}],
        stop_reason="end_turn",
        usage=Usage(input_tokens=0, output_tokens=0)
    )
```

## 总结

这个代理服务器通过以下关键技术实现了Claude API到OpenAI/Gemini API的无缝转换：

1. **格式转换**：处理不同API之间的结构差异
2. **内容块处理**：将Claude的复杂内容块转换为OpenAI/Gemini支持的格式
3. **工具调用映射**：转换不同的工具定义和调用格式
4. **流式响应转换**：实现复杂的流式事件格式转换
5. **智能模型映射**：根据配置自动选择合适的目标模型
6. **错误处理**：提供健壮的错误处理和降级机制

通过LiteLLM库的统一接口，代理能够支持多个AI提供商，同时保持与Claude客户端的完全兼容性。
