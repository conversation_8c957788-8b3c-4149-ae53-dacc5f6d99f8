# Required API Keys
ANTHROPIC_API_KEY="your-anthropic-api-key" # Needed if proxying *to* Anthropic
OPENAI_API_KEY="sk-..."
GEMINI_API_KEY="your-google-ai-studio-key"

# Optional: Provider Preference and Model Mapping
# Controls which provider (google or openai) is preferred for mapping haiku/sonnet.
# Defaults to openai if not set.
PREFERRED_PROVIDER="openai"

# Optional: Specify the exact models to map haiku/sonnet to.
# If PREFERRED_PROVIDER=google, these MUST be valid Gemini model names known to the server.
# Defaults to gemini-2.5-pro-preview-03-25 and gemini-2.0-flash if PREFERRED_PROVIDER=google.
# Defaults to gpt-4.1 and gpt-4.1-mini if PREFERRED_PROVIDER=openai.
# BIG_MODEL="gpt-4.1"
# SMALL_MODEL="gpt-4.1-mini"

# Example Google mapping:
# PREFERRED_PROVIDER="google"
# BIG_MODEL="gemini-2.5-pro-preview-03-25"
# SMALL_MODEL="gemini-2.0-flash" 